package com.mira.job.schedule.apiplatform;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.user.enums.UserEventEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.OauthUserConnectionsDTO;
import com.mira.job.dal.dao.master.OauthUserConnectionsDAO;
import com.mira.job.dal.dao.master.OpenWebhookDAO;
import com.mira.job.dal.entity.master.OpenWebhookEntity;
import com.mira.redis.cache.RedisComponent;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 用户事件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserEventSchedule {
    @Resource
    private OpenWebhookDAO openWebhookDAO;
    @Resource
    private OauthUserConnectionsDAO oauthUserConnectionsDAO;
    @Resource
    private RedisComponent redisComponent;

    private final static String USER_CYCLE_PHASE = "user_cycle_phase:";

    @XxlJob("userEventHandler")
    public void userEventHandler() {
        log.info("userEventHandler: start execute");
        run();
        log.info("userEventHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        // open_webhook
        Set<String> openWebhookClientIDList = openWebhookDAO.listByEventType(UserEventEnum.USER_PHASE_CHANGE.getEvent())
                .stream().map(OpenWebhookEntity::getClientId).collect(Collectors.toSet());

        int queryBatch = 5000;
        long pageSize = 0;
        long recordCount = oauthUserConnectionsDAO.getCount();

        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);

        while (recordCount > 0) {
            long finalPageSize = pageSize;
            CompletableFuture.runAsync(() -> {
                try {
                    List<OauthUserConnectionsDTO> oauthUserConnectionsDTOS = oauthUserConnectionsDAO.listByStatus("active", finalPageSize, queryBatch);
                    if (CollectionUtils.isNotEmpty(oauthUserConnectionsDTOS)) {
                        handle(oauthUserConnectionsDTOS, openWebhookClientIDList);
                    }
                } finally {
                    cd.countDown();
                }
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("get user event error occurred", ex);
                return null;
            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void handle(List<OauthUserConnectionsDTO> oauthUserConnectionsDTOS, Set<String> openWebhookClientIDList) {
        for (OauthUserConnectionsDTO oauthUserConnectionsDTO : oauthUserConnectionsDTOS) {
            if (!openWebhookClientIDList.contains(oauthUserConnectionsDTO.getClientId())) {
                continue;
            }
            Long userId = oauthUserConnectionsDTO.getUserId();
            String timeZone = oauthUserConnectionsDTO.getTimeZone();
            String cycleData = oauthUserConnectionsDTO.getCycleData();
            if (StringUtils.isBlank(cycleData)) {
                continue;
            }
            String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cycleData, CycleDataDTO.class);
            CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
            if (currentCycleData == null) {
                continue;
            }
            String currentPhase = getPhase(today, currentCycleData).getName();
            String key = USER_CYCLE_PHASE + userId;
            String previousPhase = redisComponent.get(key);
            if (StringUtils.isBlank(previousPhase)) {
                redisComponent.setEx(key, currentPhase, 30, TimeUnit.DAYS);
                continue;
            }
            if (currentPhase.equals(previousPhase)) {
                continue;
            }
            redisComponent.setEx(key, currentPhase, 30, TimeUnit.DAYS);
            log.info("user:{}, phase change, previous:{}, current:{}", userId, previousPhase, currentPhase);
            publishEvent(userId);
        }
    }

    private PhaseEnum getPhase(String day, CycleDataDTO cycle) {
        Predicate<CycleDataDTO> menstrualPhase = cycleData
                -> StringUtils.isNotEmpty(cycleData.getDate_period_start()) && StringUtils.isNotEmpty(cycleData.getDate_period_end())
                && LocalDateUtil.isBetweenDateAndEqualLeft(day, cycleData.getDate_period_start(), cycleData.getDate_period_end());

        Predicate<CycleDataDTO> fertilePhase = cycleData
                -> StringUtils.isNotEmpty(cycleData.getDate_FW_start()) && StringUtils.isNotEmpty(cycleData.getDate_FW_end())
                && LocalDateUtil.isBetweenDateAndEqualLeft(day, cycleData.getDate_FW_start(), cycleData.getDate_FW_end());

        Predicate<CycleDataDTO> lutealPhase = cycleData
                -> StringUtils.isNotEmpty(cycleData.getDate_ovulation())
                && LocalDateUtil.minusToDay(day, cycleData.getDate_ovulation()) > 0;

        if (menstrualPhase.test(cycle)) {
            return PhaseEnum.MENSTRUAL_PHASE;
        }

        if (fertilePhase.test(cycle)) {
            return PhaseEnum.FERTILE_WINDOW;
        }

        if (lutealPhase.test(cycle)) {
            return PhaseEnum.LUTEAL_PHASE;
        }

        return PhaseEnum.FOLLICULAR_PHASE;
    }

    @Getter
    enum PhaseEnum {
        MENSTRUAL_PHASE(0, "Menstrual Phase", "月经期"),
        FOLLICULAR_PHASE(1, "Follicular phase", "卵泡期"),
        FERTILE_WINDOW(2, "Fertile window", "排卵期"),
        LUTEAL_PHASE(3, "Luteal phase", "黄体期")
        ;

        private final int type;
        private final String name;
        private final String desc;

        PhaseEnum(int type, String name, String desc) {
            this.type = type;
            this.name = name;
            this.desc = desc;
        }
    }

    private void publishEvent(Long userId) {
        // pub event
        String eventId = UUID.randomUUID().toString();
        Map<String, String> event = new HashMap<>();
        event.put("user_id", userId.toString());
        event.put("event", UserEventEnum.USER_PHASE_CHANGE.getEvent());
        event.put("event_id", eventId);

        redisComponent.publish("user_events", JsonUtil.toJson(event));
        log.info("publish user_events, user_id:{}, event:{}, event_id:{}",
                userId, UserEventEnum.USER_PHASE_CHANGE.getEvent(), eventId);
    }
}

package com.mira.job.schedule.testing.push;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestingProductDayDTO;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.AppUserAlgorithmResultDTO;
import com.mira.job.dal.dao.master.AppUserAlgorithmResultDAO;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * testing day tomorrow push
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TestingDayTomorrowPushSchedule extends AbstractSchedule {
    @Resource
    private CommonManager commonManager;
    @Resource
    private JobManager jobManager;
    @Resource
    private AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;

    /**
     * 用户当地时间晚上9点左右
     */
    private final static String LOCAL_NOTIFICATION_TIME_START = "20:50:00";
    private final static String LOCAL_NOTIFICATION_TIME_END = "21:15:00";

    @XxlJob("testingDayTomorrowPushHandler")
    public void testingDayTomorrowPushHandler() {
        log.info("testingDayTomorrowPushHandler: start execute");
        run();
        log.info("testingDayTomorrowPushHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        Map<Long, AppUserInfoEntity> allUserInfo = jobManager.getAllUserInfo();

        int queryBatch = 10000;
        long pageSize = 0;
        long recordCount = appUserAlgorithmResultDAO.getCount();
        // batch query
        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        while (recordCount > 0) {
            String sql = "select a.user_id,a.cycle_data,a.hormone_data,info.time_zone,info.tracking_menopause " +
                    "from app_user_algorithm_result a " +
                    "join (select id from app_user_algorithm_result where deleted=0 order by id asc limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id " +
                    "join app_user_info info on a.user_id=info.user_id " +
                    "where a.deleted=0";
            CompletableFuture.runAsync(() -> {
                List<AppUserAlgorithmResultDTO> appUserAlgorithmResultDTOS = appUserAlgorithmResultDAO.getBaseMapper().listBySql(sql);
                if (CollectionUtils.isNotEmpty(appUserAlgorithmResultDTOS)) {
                    push(appUserAlgorithmResultDTOS, allUserInfo);
                }
                cd.countDown();
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("get algorithm result error occurred, sql:{}", sql, ex);
                return null;
            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        // wait future
        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void push(List<AppUserAlgorithmResultDTO> algorithmResultDTOS,
                      Map<Long, AppUserInfoEntity> allUserInfo) {
        // Push Map，value:user_id list
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (AppUserAlgorithmResultDTO algorithmResultDTO : algorithmResultDTOS) {
            Long userId = algorithmResultDTO.getUserId();
            String timeZone = algorithmResultDTO.getTimeZone();
            Integer trackingMenopause = algorithmResultDTO.getTrackingMenopause();

            // user reminder info
            UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
            if (userReminderInfo == null) {
                continue;
            }
            // remind switch
            if (0 == userReminderInfo.getRemindFlag()) {
                continue;
            }
            // testing flag
            if (0 == userReminderInfo.getTestingScheduleFlag()) {
                continue;
            }
            // 用户当地时间
            String userLocalDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            // 判断时间是否在设定的通知时间段内
            if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
                continue;
            }
            // 周期信息、Hormone信息
            List<CycleDataDTO> cycleDataDTOS;
            List<HormoneDTO> hormoneDTOS;
            try {
                cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
                hormoneDTOS = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
            } catch (Exception e) {
                continue;
            }
            // 必须是测试过的用户
            if (CollectionUtils.isEmpty(hormoneDTOS)) {
                continue;
            }
            // 必须30天内有测试过
            HormoneDTO lastHormoneDTO = hormoneDTOS.get(hormoneDTOS.size() - 1);
            int minusToDay = LocalDateUtil.minusToDay(userLocalDate, lastHormoneDTO.getTest_time());
            if (minusToDay > 30) {
                continue;
            }

            CycleDataDTO curentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);
            handle(userId, userLocalDate.substring(0, 10), curentCycleData, trackingMenopause, firebasePushMap);
        }

        // push
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("TestingDayTomorrowPushSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, convertToPushUserInfoDTO(allUserInfo), null);
    }

    private void handle(Long userId, String userLocalDate, CycleDataDTO currentCycleData,
                        Integer trackingMenopause,
                        Map<FirebasePushDTO, List<Long>> firebasePushMap) {
        // 如果测试日期列表为空，直接返回
        if (currentCycleData.getTesting_day_list() == null) {
            return;
        }

        String tomorrow = LocalDateUtil.plusDay(userLocalDate, 1, DatePatternConst.DATE_PATTERN);
        TestingProductDayDTO testingDays = currentCycleData.getTesting_day_list();

        // 测试日期检查
        boolean isMaxTestingDay = isAnyProductContainsTomorrow(
                Arrays.asList(testingDays.getProduct03(), testingDays.getProduct09(), testingDays.getProduct12()),
                tomorrow
        );
        boolean isOvumTestingDay = containsTomorrow(testingDays.getProduct16(), tomorrow);

        // 处理推送逻辑
        if (isMaxTestingDay && isOvumTestingDay) {
            sendPushNotification(userId, userLocalDate, firebasePushMap,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_OVUM_TOMORROW);
        } else if (isMaxTestingDay) {
            sendPushNotification(userId, userLocalDate, firebasePushMap,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_MAX_TOMORROW);
        } else if (isOvumTestingDay && Objects.equals(trackingMenopause, 1)) {
            sendPushNotification(userId, userLocalDate, firebasePushMap,
                    NotificationDefineEnum.TESTING_DAY_REMINDER_OVUM_TOMORROW);
        }
    }

    private boolean isAnyProductContainsTomorrow(List<List<String>> products, String tomorrow) {
        return products.stream()
                .anyMatch(product -> containsTomorrow(product, tomorrow));
    }

    private boolean containsTomorrow(List<String> product, String tomorrow) {
        return CollectionUtils.isNotEmpty(product) && product.contains(tomorrow);
    }

    private void sendPushNotification(Long userId, String userLocalDate,
                                      Map<FirebasePushDTO, List<Long>> firebasePushMap,
                                      NotificationDefineEnum notificationDefine) {
        String pushName = notificationDefine.name();
        if (jobManager.checkUserCurrentDayPush(userId, pushName, userLocalDate)) {
            return;
        }

        FirebasePushDTO pushDTO = buildPushDTO(userId, userLocalDate, notificationDefine.getDefineId(), pushName);
        firebasePushMap.computeIfAbsent(pushDTO, k -> new ArrayList<>()).add(userId);
    }

    private FirebasePushDTO buildPushDTO(Long userId, String userLocalDate, long defineId, String pushName) {
        FirebasePushDTO pushDTO = FirebaseBuildUtil.buildPushNotification(
                commonManager.getNotificationDefine(defineId),
                false
        );
        jobManager.markUserCurrentDayPush(userId, pushName, userLocalDate);
        return pushDTO;
    }
}

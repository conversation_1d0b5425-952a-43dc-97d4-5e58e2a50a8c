package com.mira.job.schedule.apiplatform;

import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.dal.dao.master.OauthUserConnectionsDAO;
import com.mira.job.dal.entity.master.OauthUserConnectionsEntity;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * 更新用户是否活跃
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserStatusSchedule {
    @Resource
    private OauthUserConnectionsDAO oauthUserConnectionsDAO;

    @XxlJob("userStatusHandler")
    public void userStatusHandler() {
        log.info("userStatusHandler: start execute");
        run();
        log.info("userStatusHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        int queryBatch = 5000;
        long pageSize = 0;
        long recordCount = oauthUserConnectionsDAO.getCount();

        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);

        while (recordCount > 0) {
            long finalPageSize = pageSize;
            CompletableFuture.runAsync(() -> {
                try {
                    List<OauthUserConnectionsEntity> oauthUserConnectionsEntities = oauthUserConnectionsDAO.listBatch("active", finalPageSize, queryBatch);
                    if (CollectionUtils.isNotEmpty(oauthUserConnectionsEntities)) {
                        handle(oauthUserConnectionsEntities);
                    }
                } finally {
                    cd.countDown();
                }
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("get user connection error occurred", ex);
                return null;
            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void handle(List<OauthUserConnectionsEntity> oauthUserConnectionsEntities) {
        List<OauthUserConnectionsEntity> updateList = new ArrayList<>();
        for (OauthUserConnectionsEntity oauthUserConnectionsEntity : oauthUserConnectionsEntities) {
            Double tokenExpiresAt = oauthUserConnectionsEntity.getTokenExpiresAt();
            if (tokenExpiresAt - System.currentTimeMillis() < 0) {
                log.info("user inactive, token expired, expire:{}, email:{}", tokenExpiresAt, oauthUserConnectionsEntity.getEmail());
                // update oauth_user_connections status to inactive
                oauthUserConnectionsEntity.setStatus("inactive");
                oauthUserConnectionsEntity.setUpdatedAt(System.currentTimeMillis());
                updateList.add(oauthUserConnectionsEntity);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            oauthUserConnectionsDAO.updateBatchById(updateList);
            log.info("update user status to inactive, count:{}", updateList.size());
        }
    }
}

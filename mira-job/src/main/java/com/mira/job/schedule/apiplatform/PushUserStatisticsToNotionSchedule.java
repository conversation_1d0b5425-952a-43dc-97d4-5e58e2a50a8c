package com.mira.job.schedule.apiplatform;

import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.OauthAuthorizeLogDTO;
import com.mira.job.consts.dto.notion.NotionApiPlatformRequest;
import com.mira.job.consts.dto.notion.NotionRequest;
import com.mira.job.consts.dto.notion.NotionResponse;
import com.mira.job.consts.dto.notion.filter.NotionFilter;
import com.mira.job.consts.dto.notion.filter.Property;
import com.mira.job.consts.dto.notion.filter.RichText;
import com.mira.job.dal.dao.master.OauthAuthorizeLogDAO;
import com.mira.web.util.OkHttpUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 推送用户统计信息到Notion
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PushUserStatisticsToNotionSchedule {
    @Resource
    private OauthAuthorizeLogDAO oauthAuthorizeLogDAO;

    /**
     * notion config
     */
    private final static String NOTION_API_VERION = "2022-06-28";
    private final static String NOTION_URL = "https://api.notion.com/v1";
    //    private final static String NOTION_KEY = "**************************************************";
    private final static String NOTION_KEY = "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";
    //    private final static String NOTION_DATABASE_ID = "xxx";
    private final static String NOTION_DATABASE_ID = "24ff761ed6ba80efa4bad67f442bf31e";

    @XxlJob("pushUserStatisticsHandler")
    public void pushUserStatisticsHandler() {
        log.info("pushUserStatisticsHandler: start execute");
        run();
        log.info("pushUserStatisticsHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        int queryBatch = 5000;
        long pageSize = 0;
        long recordCount = oauthAuthorizeLogDAO.getBaseMapper().count();

//        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
//        CountDownLatch cd = new CountDownLatch((int) cdCount);

        while (recordCount > 0) {
            long finalPageSize = pageSize;
//            CompletableFuture.runAsync(() -> {
//                try {
                    List<OauthAuthorizeLogDTO> oauthAuthorizeLogDTOS = oauthAuthorizeLogDAO.getBaseMapper().list(finalPageSize, queryBatch);
                    if (CollectionUtils.isNotEmpty(oauthAuthorizeLogDTOS)) {
                        handle(oauthAuthorizeLogDTOS);
                    }
//                } finally {
//                    cd.countDown();
//                }
//            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
//                log.error("get oauth authorize log error", ex);
//                return null;
//            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

//        try {
//            cd.await();
//        } catch (InterruptedException ex) {
//            log.error(ex.getMessage(), ex);
//        }
    }

    private void handle(List<OauthAuthorizeLogDTO> oauthAuthorizeLogDTOS) {
        // db内的合作方名称
        Set<String> dbPartnerNameSet = oauthAuthorizeLogDTOS.stream()
                .map(obj -> {
                    String clientId = obj.getClientId();
                    String tenantCode = obj.getTenantCode();
                    if (StringUtils.isNotBlank(clientId)) {
                        return clientId.split("_")[0];
                    } else {
                        return tenantCode.split("_")[0];
                    }
                }).collect(Collectors.toSet());

        // headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Notion-Version", NOTION_API_VERION);
        headers.put("Authorization", "Bearer " + NOTION_KEY);

        // 记录database内每个page的id，key:partnerName，value:page_id
        Map<String, String> pageIds = new HashMap<>();

        // 比对并推送还未push到notion的合作方
        pushNewRecord(headers, dbPartnerNameSet, pageIds);

        // 更新db最新数据到notion page
//        updateRecord(headers, oauthAuthorizeLogDTOS, pageIds);
    }

    private void pushNewRecord(Map<String, String> headers,
                               Set<String> dbPartnerNameSet,
                               Map<String, String> pageIds) {
        try {
            // notion上的合作方名称
            Set<String> notionPartnerNameSet = new HashSet<>();

            for (;;) {
                // 先拉取已经push的合作方，和db进行比对，未push的新增上去
                NotionRequest notionRequest = new NotionRequest();
                notionRequest.setFilter(getNotionFilter());

                String response = OkHttpUtil.post(NOTION_URL.concat("/databases/")
                        .concat(NOTION_DATABASE_ID).concat("/query?filter_properties=title"), JsonUtil.toJson(notionRequest), headers);
                NotionResponse notionResponse = JsonUtil.toObject(response, NotionResponse.class);
                List<NotionResponse.Results> results = notionResponse.getResults();

                for (NotionResponse.Results result : results) {
                    Map<String, Object> properties = result.getProperties();
                    Map<String, Object> nameObject = (Map<String, Object>) properties.get("Name");
                    List<Map<String, Object>> titleList = (List<Map<String, Object>>) nameObject.get("title");
                    Map<String, Object> titleObject = titleList.get(0);
                    String title = (String) titleObject.get("plain_text");
                    notionPartnerNameSet.add(title);
                    pageIds.put(title, result.getId());
                }

                if (notionResponse.getHas_more()) {
                    String nextCursor = notionResponse.getNext_cursor();
                    notionRequest.setStart_cursor(nextCursor);
                } else {
                    break;
                }
            }

            // 比对，未push的新增上去
            Set<String> diffSet = dbPartnerNameSet.stream()
                    .filter(obj -> !notionPartnerNameSet.contains(obj))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(diffSet)) {
                for (String partnerName : diffSet) {
                    NotionApiPlatformRequest request = new NotionApiPlatformRequest();
                    request.setParent(new NotionApiPlatformRequest.Parent().setDatabase_id(NOTION_DATABASE_ID));
                    request.setProperties(buildInitProperties(partnerName));
                    String responseStr = OkHttpUtil.post(NOTION_URL.concat("/pages"), JsonUtil.toJson(request), headers);
                    Map<String, Object> response = JsonUtil.toObject(responseStr, Map.class);
                    pageIds.put(partnerName, (String) response.get("id"));
                }
            }

        } catch (Exception e) {
            log.error("push new record error", e);
        }
    }

    private void updateRecord(Map<String, String> headers,
                              List<OauthAuthorizeLogDTO> oauthAuthorizeLogDTOS,
                              Map<String, String> pageIds) {
        try {
            Map<String, Set<OauthAuthorizeLogDTO>> map_1 = new HashMap<>();
            Map<String, Set<OauthAuthorizeLogDTO>> map_2 = new HashMap<>();

            for (OauthAuthorizeLogDTO oauthAuthorizeLogDTO : oauthAuthorizeLogDTOS) {
                String clientId = oauthAuthorizeLogDTO.getClientId();
                String tenantCode = oauthAuthorizeLogDTO.getTenantCode();

                // Authorization Type: User Login
                if (StringUtils.isNotBlank(clientId)) {
                    String partnerName = oauthAuthorizeLogDTO.getClientId().split("_")[0];
                    map_1.putIfAbsent(partnerName, new HashSet<>());
                    map_1.get(partnerName).add(oauthAuthorizeLogDTO);
                }
                // Authorization Type: Email Confirmation
                if (StringUtils.isNotBlank(tenantCode)) {
                    String partnerName = oauthAuthorizeLogDTO.getTenantCode().split("_")[0];
                    map_2.putIfAbsent(partnerName, new HashSet<>());
                    map_2.get(partnerName).add(oauthAuthorizeLogDTO);
                }
            }

            // request
            NotionApiPlatformRequest notionApiPlatformRequest = new NotionApiPlatformRequest();
            notionApiPlatformRequest.setParent(new NotionApiPlatformRequest.Parent().setDatabase_id(NOTION_DATABASE_ID));

            // Authorization Type: User Login
            for (Map.Entry<String, Set<OauthAuthorizeLogDTO>> entry : map_1.entrySet()) {
                String partnerName = entry.getKey();
                String pageId = pageIds.get(partnerName);
                Set<OauthAuthorizeLogDTO> logSet = entry.getValue();

                // properties
                notionApiPlatformRequest.setProperties(buildProperties(partnerName, "User Login", logSet));
                // push
                OkHttpUtil.post(NOTION_URL.concat("/pages/").concat(pageId), JsonUtil.toJson(notionApiPlatformRequest), headers);
            }

            // Authorization Type: Email Confirmation
            for (Map.Entry<String, Set<OauthAuthorizeLogDTO>> entry : map_2.entrySet()) {
                String partnerName = entry.getKey();
                String pageId = pageIds.get(partnerName);
                Set<OauthAuthorizeLogDTO> logSet = entry.getValue();

                // properties
                notionApiPlatformRequest.setProperties(buildProperties(partnerName, "Email Confirmation", logSet));
                // push
                OkHttpUtil.post(NOTION_URL.concat("/pages/").concat(pageId), JsonUtil.toJson(notionApiPlatformRequest), headers);
            }

        } catch (Exception e) {
            log.error("update record error", e);
        }
    }

    @NotNull
    private NotionFilter getNotionFilter() {
        RichText richText = new RichText();
        richText.set_not_empty(true);

        Property titleProperty = new Property();
        titleProperty.setProperty("Name");
        titleProperty.setRich_text(richText);
        List<Property> and = List.of(titleProperty);

        NotionFilter filter = new NotionFilter();
        filter.setAnd(and);
        return filter;
    }

    private Map<String, Object> buildInitProperties(String partnerName) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("Name", Map.of("title", List.of(Map.of("text", Map.of("content", partnerName)))));
        properties.put("Authorization Type", Map.of("rich_text", List.of(Map.of("text", Map.of("content", "")))));
        properties.put("Total number of users", Map.of("number", 0));
        properties.put("Active users (7 days)", Map.of("number", 0));
        return properties;
    }

    private Map<String, Object> buildProperties(String partnerName,
                                                String authorizationType,
                                                Set<OauthAuthorizeLogDTO> logSet) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("Name", Map.of("title", List.of(Map.of("text", Map.of("content", partnerName)))));
        properties.put("Authorization Type", Map.of("rich_text", List.of(Map.of("text", Map.of("content", authorizationType)))));
        properties.put("Total number of users", Map.of("number", logSet.size()));
        properties.put("Active users (7 days)", Map.of("number",
                logSet.stream().filter(log -> {
                    String today = ZoneDateUtil.format(log.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
                    return LocalDateUtil.minusToDay(today, log.getIpModifyTimeStr()) <= 7;
                }).count()
        ));

        return properties;
    }
}

package com.mira.job.controller;

import com.mira.job.schedule.apiplatform.PushUserStatisticsToNotionSchedule;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
public class DemoController {
    @Resource
    private PushUserStatisticsToNotionSchedule pushUserStatisticsToNotionSchedule;

    @GetMapping("/demo")
    public void demo() {
        pushUserStatisticsToNotionSchedule.pushUserStatisticsHandler();
    }
}

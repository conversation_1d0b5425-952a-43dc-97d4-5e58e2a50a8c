package com.mira.job.consts.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class OauthAuthorizeLogDTO implements Serializable {
    /**
     * client id
     */
    private String clientId;

    /**
     * tenant code
     */
    private String tenantCode;

    /**
     * email
     */
    private String email;

    /**
     * ip modify time
     */
    private String ipModifyTimeStr;

    /**
     * time zone
     */
    private String timeZone;
}

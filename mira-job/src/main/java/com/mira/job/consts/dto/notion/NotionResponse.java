package com.mira.job.consts.dto.notion;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * notion response
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class NotionResponse {
    private String object;
    private List<Results> results;
    private Boolean has_more;
    private String next_cursor;

    @Getter
    @Setter
    public static class Results {
        private String id;
        private Map<String, Object> properties;
    }
}

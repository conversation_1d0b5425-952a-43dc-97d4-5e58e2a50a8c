package com.mira.job.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.NotificationConditionEnum;
import com.mira.job.consts.enums.ReminderEnum;
import com.mira.job.dal.dao.master.*;
import com.mira.job.dal.entity.master.*;
import com.mira.redis.cache.RedisComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * App User Manager
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JobManager {
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserReminderDAO appUserReminderDAO;
    private final UserKlaviyoListDAO userKlaviyoListDAO;
    private final ReportGoodUsersDAO reportGoodUsersDAO;
    private final UserProductTrialDAO userProductTrialDAO;
    private final AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;
    private final RedisComponent redisComponent;

    /**
     * 每次查询的记录数
     */
    private final int queryBatch = 10000;

    /**
     * 用户信息缓存
     */
    private final static Cache<Long, AppUserReminderEntity> userRemindCache;

    static {
        long expire = Duration.between(LocalDateTime.now(),
                LocalDateTime.now().plusHours(6)).getSeconds();
        // 过期时间，6小时
        userRemindCache = CacheBuilder.newBuilder()
                .expireAfterWrite(expire, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 查询所有用户信息 app_user
     *
     * @return k:user_id, v:AppUserEntity
     */
    public Map<Long, AppUserEntity> getAllUser() {
        // 用户列表
        List<AppUserEntity> allUserList = new CopyOnWriteArrayList<>();

        long pageSize = 0; // 分页参数
        long userCount = appUserDAO.getCount();
        log.info("用户总数：{}", userCount);
        // 分批查询
        long cdCount = userCount % queryBatch == 0 ? userCount / queryBatch : userCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        while (userCount > 0) {
            // app_user
            String sql = "select a.id,a.email,a.current_ip,a.country_code,a.continent_code,a.time_zone " +
                    "from app_user a " +
                    "join (select id from app_user where deleted=0 limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";
            CompletableFuture.runAsync(() -> {
                List<AppUserEntity> appUserList = appUserDAO.getBaseMapper().listBySql(sql);
                if (CollectionUtils.isNotEmpty(appUserList)) {
                    allUserList.addAll(appUserList);
                }
                cd.countDown();
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("getAllUser error occurred, sql:{}", sql, ex);
                return null;
            });

            pageSize += queryBatch;
            userCount -= queryBatch;
        }

        // wait future
        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }

        return allUserList.stream().collect(Collectors.toMap(AppUserEntity::getId, Function.identity()));
    }

    /**
     * 查询所有用户信息 app_user_info
     *
     * @return k:user_id, v:AppUserInfoEntity
     */
    public Map<Long, AppUserInfoEntity> getAllUserInfo() {
        // 用户信息列表
        List<AppUserInfoEntity> allUserInfoList = new CopyOnWriteArrayList<>();

        long pageSize = 0; // 分页参数
        long userCount = appUserInfoDAO.getCount();
        log.info("用户总数：{}", userCount);
        // 分批查询
        long cdCount = userCount % queryBatch == 0 ? userCount / queryBatch : userCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        while (userCount > 0) {
            // app_user_info
            String sql = "select a.user_id,a.goal_status,a.is_oft,a.remind_flag," +
                    "a.birth_year,a.birth_month,a.birth_of_day,a.tracking_menopause,a.tracking_menopause_date," +
                    "a.defined_irregular_cycle,a.push_token,a.platform,a.bind_version,a.time_zone " +
                    "from app_user_info a " +
                    "join (select id from app_user_info where deleted=0 order by id asc limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";
            CompletableFuture.runAsync(() -> {
                List<AppUserInfoEntity> appUserInfoList = appUserInfoDAO.getBaseMapper().listBySql(sql);
                if (CollectionUtils.isNotEmpty(appUserInfoList)) {
                    allUserInfoList.addAll(appUserInfoList);
                }
                cd.countDown();
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("getAllUserInfo error occurred, sql:{}", sql, ex);
                return null;
            });

            pageSize += queryBatch;
            userCount -= queryBatch;
        }

        // wait future
        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }

        return allUserInfoList.stream().collect(Collectors.toMap(AppUserInfoEntity::getUserId, Function.identity()));
    }

    /**
     * 查询所有用户Klaviyo用到的算法数据，数据由算法提供
     *
     * @return allUserKlaviyoList
     */
    public List<UserKlaviyoListEntity> getAllUserKlaviyo() {
        List<UserKlaviyoListEntity> allUserKlaviyoList = new ArrayList<>();

        long cycle = 1;
        long pageSize = 0;
        long recordCount = userKlaviyoListDAO.getCount();
        log.info("user_klavyio_list记录总数：{}", recordCount);

        while (recordCount > 0) {
            log.info("表名[user_klavyio_list]，第{}次查询，剩余数量：{}", cycle++, recordCount);
            String sql = "select a.user_id,a.test_taken_ratio,a.is_amazon,good_user,has_analyzer_issue,has_wand_issue,has_usage_issue " +
                    "from user_klavyio_list a " +
                    "join (select id from user_klavyio_list limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";

            allUserKlaviyoList.addAll(userKlaviyoListDAO.getBaseMapper().listBySql(sql));

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        return allUserKlaviyoList;
    }

    /**
     * 查询亚马逊用户数据，数据由算法提供
     *
     * @return allUserAmazonList
     */
    public List<ReportGoodUsersEntity> getAllUserAmazon() {
        List<ReportGoodUsersEntity> allUserAmazonList = new ArrayList<>();

        long cycle = 1;
        long pageSize = 0;
        long queryBatch = 10000;
        long recordCount = reportGoodUsersDAO.getCount();
        log.info("report_good_users记录总数：{}", recordCount);

        while (recordCount > 0) {
            log.info("表名[report_good_users]，第{}次查询，剩余数量：{}", cycle++, recordCount);
            String sql = "select a.user_id,a.email " +
                    "from report_good_users a " +
                    "join (select id from report_good_users where is_amazon=1 " +
                    "limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";

            allUserAmazonList.addAll(reportGoodUsersDAO.getBaseMapper().listBySql(sql));

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        return allUserAmazonList;
    }

    /**
     * 获取算法结果表缓存
     *
     * @param userId 用户编号
     * @return AlgorithmResultDTO
     */
    public AlgorithmResultDTO getCacheAlgorithmResult(Long userId) {
        String cacheKey = RedisCacheKeyConst.USER_ALGORITHM_RESULT + userId;
        AlgorithmResultDTO algorithmResultDTO = redisComponent.get(cacheKey, AlgorithmResultDTO.class);
        if (ObjectUtils.isEmpty(algorithmResultDTO)) {
            AppUserAlgorithmResultEntity userAlgorithmResult = appUserAlgorithmResultDAO.getByUserId(userId);
            if (userAlgorithmResult == null) {
                return null;
            }
            return BeanUtil.toBean(userAlgorithmResult, AlgorithmResultDTO.class);
        }
        return algorithmResultDTO;
    }

    /**
     * 根据通知类型查询用户通知开关
     *
     * @param userId       用户编号
     * @param reminderEnum 开关名称
     * @return true/false
     */
    public boolean userRemindOpen(Long userId, ReminderEnum reminderEnum) {
        UserReminderInfoDTO userReminderInfo = getUserReminderInfo(userId);
        if (userReminderInfo == null) {
            return false;
        }
        switch (reminderEnum) {
            case CYCLE_PHASE_SWITCH:
                return 1 == userReminderInfo.getCyclePhaseFlag();
            case PURCHASE_SWITCH:
                return 1 == userReminderInfo.getPurchaseFlag();
            case HIDE_CONTENT_SWITCH:
                return 1 == userReminderInfo.getHideContentFlag();
            default:
                return false;
        }
    }

    /**
     * 检验用户是否发送firebase
     *
     * @param userId 用户编号
     * @return true/false
     */
    public boolean checkRemindFlag(Long userId) {
        UserReminderInfoDTO userReminderInfo = getUserReminderInfo(userId);
        if (userReminderInfo == null) {
            return false;
        }
        // check 总开关
        return 0 != userReminderInfo.getRemindFlag();
    }

    /**
     * 获取用户通知开关信息
     *
     * @param userId 用户编号
     * @return AppUserReminderEntity
     */
    public UserReminderInfoDTO getUserReminderInfo(Long userId) {
        userRemindCache.cleanUp();
        ConcurrentMap<Long, AppUserReminderEntity> reminderEntityConcurrentMap = userRemindCache.asMap();
        if (reminderEntityConcurrentMap.isEmpty()) {
            appUserReminderDAO.list().forEach(reminder -> userRemindCache.put(reminder.getUserId(), reminder));
        }

        AppUserReminderEntity result = userRemindCache.asMap().get(userId);
        if (result == null) {
            return null;
        }
        UserReminderInfoDTO userReminderInfoDTO = new UserReminderInfoDTO();
        BeanUtil.copyProperties(result, userReminderInfoDTO);
        if (ObjectUtils.isEmpty(userReminderInfoDTO.getTestingScheduleRemindTime())) {
            userReminderInfoDTO.setTestingScheduleRemindTime(ZoneDateUtil.timestamp(result.getTimeZone(),
                    "2023-03-03 05:00:00", DatePatternConst.DATE_TIME_PATTERN));
        }
        if (ObjectUtils.isEmpty(userReminderInfoDTO.getBbtTestingRemindTime())) {
            userReminderInfoDTO.setBbtTestingRemindTime(ZoneDateUtil.timestamp(result.getTimeZone(),
                    "2023-03-03 05:00:00", DatePatternConst.DATE_TIME_PATTERN));
        }

        return userReminderInfoDTO;
    }

    /**
     * 更新 test schedule flag
     *
     * @param userId           用户编号
     * @param testScheduleFlag test schedule flag
     */
    public void updateTestScheduleFlag(Long userId, Integer testScheduleFlag) {
        ConcurrentMap<Long, AppUserReminderEntity> reminderEntityConcurrentMap = userRemindCache.asMap();
        if (reminderEntityConcurrentMap.isEmpty()) {
            appUserReminderDAO.list().forEach(reminder -> userRemindCache.put(reminder.getUserId(), reminder));
        }
        AppUserReminderEntity result = userRemindCache.asMap().get(userId);
        if (result != null) {
            result.setTestingScheduleFlag(testScheduleFlag);
        }
    }

    /**
     * 标记用户当天已经推送
     *
     * @param userId        用户编号
     * @param pushName      推送名称
     * @param userLocalDate 用户当地日期
     */
    public void markUserCurrentDayPush(Long userId, String pushName, String userLocalDate) {
        /*
        key, pushType:user_id:month
        value, day
         */
        String month = userLocalDate.substring(5, 7);
        String key = pushName + ":" + userId + ":" + month;
        boolean isNewKey = !redisComponent.exists(key);

        userLocalDate = userLocalDate.substring(8, 10);
        long day = Long.parseLong(userLocalDate);
        redisComponent.setBit(key, day);

        // 三个月过期
        if (isNewKey) {
            redisComponent.expire(key, ThreadLocalRandom.current().nextLong(2160, 2232), TimeUnit.HOURS);
        }
    }

    /**
     * 标记用户当天已经推送
     *
     * @param userId        用户编号
     * @param pushType      推送类型，例如 Cycle Phase
     * @param userLocalDate 用户当地日期
     */
    public void markUserCurrentDayPush(Long userId, NotificationConditionEnum pushType, String userLocalDate) {
        markUserCurrentDayPush(userId, pushType.name(), userLocalDate);
    }

    /**
     * 检查用户当天是否已经推送过通知
     *
     * @param userId        用户id
     * @param pushName      推送名称
     * @param userLocalDate 用户当地日期
     * @return true/false
     */
    public boolean checkUserCurrentDayPush(Long userId, String pushName, String userLocalDate) {
        String month = userLocalDate.substring(5, 7);
        String key = pushName + ":" + userId + ":" + month;

        userLocalDate = userLocalDate.substring(8, 10);
        long value = Long.parseLong(userLocalDate);
        return redisComponent.getBit(key, value);
    }

    /**
     * 检查用户当天是否已经推送过通知
     *
     * @param userId        用户id
     * @param pushType      推送类型，例如 Cycle Phase
     * @param userLocalDate 用户当地日期
     * @return true/false
     */
    public boolean checkUserCurrentDayPush(Long userId, NotificationConditionEnum pushType, String userLocalDate) {
        return checkUserCurrentDayPush(userId, pushType.name(), userLocalDate);
    }

    /**
     * 标记用户周期内已经推送
     *
     * @param userId     用户编号
     * @param pushType   推送类型，例如 Cycle Phase
     * @param cycleIndex 周期索引
     */
    public void markUserCurrentCyclePush(Long userId, NotificationConditionEnum pushType, int cycleIndex) {
        /*
        key, pushType:user_id
        value, cycle_index
         */
        String key = pushType.name() + ":" + userId;
        boolean isNewKey = !redisComponent.exists(key);

        redisComponent.setBit(key, cycleIndex);

        // 十二个月过期
        if (isNewKey) {
            redisComponent.expire(key, ThreadLocalRandom.current().nextLong(8700, 8760), TimeUnit.HOURS);
        }
    }

    /**
     * 检查用户周期内是否已经推送过通知
     *
     * @param userId     用户id
     * @param pushType   推送类型，例如 Cycle Phase
     * @param cycleIndex 周期索引
     * @return true/false
     */
    public boolean checkUserCurrentCyclePush(Long userId, NotificationConditionEnum pushType, int cycleIndex) {
        String key = pushType.name() + ":" + userId;

        return redisComponent.getBit(key, cycleIndex);
    }

    /**
     * 标记用户周期内推送的次数
     *
     * @param userId     用户id
     * @param pushType   推送类型，例如 Cycle Phase
     * @param cycleIndex 周期索引
     */
    public void markUserCyclePushCount(Long userId, NotificationConditionEnum pushType, int cycleIndex) {
        String key = pushType.name() + ":" + userId + ":NUM:" + cycleIndex;
        Long incr = redisComponent.incr(key);

        // 三个月过期
        if (incr == 1) {
            redisComponent.expire(key, ThreadLocalRandom.current().nextLong(2160, 2232), TimeUnit.HOURS);
        }
    }

    /**
     * 获取周期内用户推送的次数
     *
     * @param userId     用户id
     * @param pushType   推送类型，例如 Cycle Phase
     * @param cycleIndex 周期索引
     * @return count
     */
    public Integer getUserCyclePushCount(Long userId, NotificationConditionEnum pushType, int cycleIndex) {
        String key = pushType.name() + ":" + userId + ":NUM:" + cycleIndex;
        Integer pushCount = redisComponent.get(key, Integer.class);
        return pushCount == null ? 0 : pushCount;
    }

    /**
     * 是否重复推送昨天的通知
     *
     * @param appUserInfo 用户信息
     * @param defineId    通知定义编号
     * @return true/false
     */
    public boolean remindTomorrow(PushUserInfoDTO appUserInfo, Long defineId) {
        /*
        key, REPEAT_PUSH:user_id:define_id
         */
        String key = "notification_repeat_push:" + appUserInfo.getUserId() + ":" + defineId;
        if (redisComponent.exists(key)) {
            // 只重复推送一次
            redisComponent.delete(key);
            return true;
        }
        return false;
    }

    /**
     * 用户是否存在产品测试表中
     *
     * @param userId   用户id
     * @param wandType 试剂类型
     * @return true/false
     */
    public boolean existProductTrial(Long userId, Integer wandType) {
        long count = userProductTrialDAO.getCount(userId, wandType);
        return count > 0;
    }

    /**
     * 用户是否在特定日期进行了经期延长操作
     *
     * @param userId 用户id
     * @param date   日期
     * @return true/false
     */
    public boolean extendPeriodMark(Long userId, String date) {
        String markKey = RedisCacheKeyConst.EXTEND_PERIOD_MARK.concat(userId.toString());
        String markDate = redisComponent.get(markKey);
        if (StringUtils.isBlank(markDate)) {
            return false;
        }
        return date.equals(markDate);
    }

    /**
     * 标记用户推送，不过期
     *
     * @param userId   用户id
     * @param pushType 推送类型
     */
    public void markUserPush(Long userId, NotificationConditionEnum pushType) {
        String key = pushType.name() + ":" + userId;
        redisComponent.set(key, "1");
    }

    /**
     * 检查用户推送
     *
     * @param userId   用户id
     * @param pushType 推送类型
     * @return true/false
     */
    public boolean checkUserPush(Long userId, NotificationConditionEnum pushType) {
        String key = pushType.name() + ":" + userId;
        return redisComponent.exists(key);
    }
}

package com.mira.job.service.manager;

import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.properties.JobProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.PooledExecutionServiceConfigurationBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * ehcache manager
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PushUserInfoCacheManager {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private JobProperties jobProperties;

    private final CacheManager cacheManager;
    private final String cacheFileName = "pushuserinfo-cache";
    private final String cacheIndexKey = "pushuserinfo-cache-index";
    
    // 分段锁，用于减少锁竞争
    private final Object[] locks = new Object[16];
    
    // 初始化分段锁
    {
        for (int i = 0; i < locks.length; i++) {
            locks[i] = new Object();
        }
    }

    public PushUserInfoCacheManager() {
        // 配置缓存，优先使用磁盘存储，只保留少量热点数据在内存中
        CacheConfiguration<String, ArrayList> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class, ArrayList.class, ResourcePoolsBuilder.newResourcePoolsBuilder()
                        // 只在堆内存中保留极少量条目（热点数据）
                        .heap(3, EntryUnit.ENTRIES)
                        // 使用堆外内存作为二级缓存，减轻堆内存压力
                        .offheap(1, MemoryUnit.GB)
                        // 主要存储在磁盘上，持久化为true
                        .disk(100, MemoryUnit.GB, true))
                // 设置过期时间
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofHours(2)))
                // 启用磁盘优先策略
                .withDiskStoreThreadPool("disk-operations", 5)
                .build();

        // 配置缓存管理器
        this.cacheManager = CacheManagerBuilder.newCacheManagerBuilder()
                // 指定持久化目录
                .with(CacheManagerBuilder.persistence("./cache"))
                // 启用更新写入模式，减少内存占用
                .using(PooledExecutionServiceConfigurationBuilder
                        .newPooledExecutionServiceConfigurationBuilder()
                        .defaultPool("default-disk-pool", 1, 5)
                        .pool("disk-operations", 1, 3)
                        .build())
                .withCache(cacheFileName, configuration)
                .build(true);
    }
    
    /**
     * 根据缓存键获取对应的分段锁
     */
    private Object getLock(String cacheIndex) {
        int hash = cacheIndex.hashCode();
        return locks[Math.abs(hash) % locks.length];
    }

    private void put(String key, ArrayList value) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.put(key, value);
    }

    private ArrayList get(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        // 添加重试机制
        ArrayList result = cache.get(key);
        if (result == null && !cacheIndexKey.equals(key)) {
            // 如果第一次读取失败，等待短暂时间后重试
            try {
                Thread.sleep(20);
                result = cache.get(key);
                if (result == null) {
                    log.warn("{} 缓存读取失败后重试仍然失败, key: {}", Thread.currentThread().getName(), key);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("{} 缓存读取重试被中断", Thread.currentThread().getName(), e);
            }
        }
        return result;
    }

    /**
     * 从缓存中移除指定键的数据
     */
    private void remove(String key) {
        Cache<String, ArrayList> cache = cacheManager.getCache(cacheFileName, String.class, ArrayList.class);
        cache.remove(key);
        log.info("{} 已从缓存中移除数据: {}", Thread.currentThread().getName(), key);
    }

    /**
     * 清空所有缓存
     * 在内存压力大时可以调用此方法释放内存
     */
    public void clearAllCache() {
        try {
            ArrayList<String> cacheIndexList = get(cacheIndexKey);
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                for (String index : cacheIndexList) {
                    remove(index);
                }
                log.info("{} 已清空所有缓存数据，共 {} 个分批", Thread.currentThread().getName(), cacheIndexList.size());
            }
            remove(cacheIndexKey);
        } catch (Exception e) {
            log.error("{} 清空缓存异常: {}", Thread.currentThread().getName(), e.getMessage(), e);
        }
    }

    /**
     * 获取缓存状态信息
     * 返回缓存中的分批数量和估计的数据量
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        try {
            ArrayList<String> cacheIndexList = get(cacheIndexKey);
            int batchCount = CollectionUtils.isEmpty(cacheIndexList) ? 0 : cacheIndexList.size();
            status.put("缓存分批数", batchCount);

            // 估算总数据量
            long totalRecords = 0;
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                for (String index : cacheIndexList) {
                    ArrayList records = get(index);
                    if (records != null) {
                        totalRecords += records.size();
                    }
                }
            }
            status.put("缓存总条目", totalRecords);
            status.put("缓存状态", batchCount > 0 ? "正常" : "未初始化");
        } catch (Exception e) {
            log.error("获取缓存状态异常: {}", e.getMessage(), e);
            status.put("缓存状态", "异常: " + e.getMessage());
        }
        return status;
    }

    public List<String> getCacheIndex() {
        String threadName = Thread.currentThread().getName();
        log.info("{} getCacheIndex开始执行", threadName);
        
        // 先检查缓存开关
        if (0 == jobProperties.getCacheSwitch()) {
            log.info("{} 缓存开关关闭，清除缓存并返回空列表", threadName);
            synchronized (PushUserInfoCacheManager.class) {
                remove(cacheIndexKey);
                // 清除所有缓存数据
                ArrayList<String> oldIndex = get(cacheIndexKey);
                if (CollectionUtils.isNotEmpty(oldIndex)) {
                    for (String index : oldIndex) {
                        remove(index);
                    }
                }
            }
            return new ArrayList<>();
        }
        
        // 使用同步块来确保并发安全地读取缓存索引
        synchronized (PushUserInfoCacheManager.class) {
            // 尝试获取缓存索引
            ArrayList cacheIndexList = get(cacheIndexKey);
            log.info("{} 从缓存获取索引列表, 大小: {}", threadName, cacheIndexList != null ? cacheIndexList.size() : 0);
            
            if (CollectionUtils.isEmpty(cacheIndexList)) {
                log.info("{} 缓存索引为空，开始初始化", threadName);
                // 直接在这里执行初始化逻辑
                initializeCache();
                
                ArrayList result = get(cacheIndexKey);
                log.info("{} 缓存初始化完成，索引大小: {}", threadName, result != null ? result.size() : 0);
                return result != null ? result : new ArrayList<>();
            }
            
            // 创建一个新的列表副本，避免并发修改问题
            return new ArrayList<>(cacheIndexList);
        }
    }

    /**
     * 初始化缓存的专用方法
     */
    private void initializeCache() {
        String threadName = Thread.currentThread().getName();
        log.info("{} initializeCache开始执行", threadName);
        
        // 分批查询参数设置
        int queryBatch = 5000;
        long pageSize = 0;
        long recordCount = appUserInfoDAO.getCount();
        log.info("{} 用户记录总数: {}", threadName, recordCount);

        // 计算分批数量
        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        long queryIndex = cdCount;
        // 使用线程安全的 CopyOnWriteArrayList
        CopyOnWriteArrayList<String> queryIndexList = new CopyOnWriteArrayList<>();

        // 分批处理
        while (recordCount > 0) {
            long finalPageSize = pageSize;
            long finalQueryIndex = queryIndex;
            CompletableFuture.runAsync(() -> {
                try {
                    // 查询数据库
                    ArrayList<PushUserInfoDTO> recordList = appUserInfoDAO.getBaseMapper().queryUserInfo(finalPageSize, queryBatch);
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        String key = String.valueOf(finalQueryIndex);
                        queryIndexList.add(key);
                        // 将数据存入缓存
                        put(key, recordList);
                        log.info("成功缓存用户数据分批: {}, 数量: {}", key, recordList.size());
                    }
                } catch (Exception e) {
                    log.error("查询用户信息异常: {}", e.getMessage(), e);
                } finally {
                    cd.countDown();
                }
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("获取用户信息异常", ex);
                return null;
            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
            queryIndex--;
        }

        // 等待所有异步任务完成
        try {
            log.info("{} 开始等待异步加载完成, 批次数: {}", threadName, cdCount);
            cd.await();
            log.info("{} 所有用户数据缓存完成，共 {} 批", threadName, cdCount);
        } catch (InterruptedException ex) {
            log.error("{} 等待缓存加载完成时被中断: {}", threadName, ex.getMessage(), ex);
        }

        // 缓存索引
        if (CollectionUtils.isNotEmpty(queryIndexList)) {
            log.info("{} 准备更新缓存索引, 当前索引数: {}", threadName, queryIndexList.size());
            // 转换为 ArrayList 以便序列化
            put(cacheIndexKey, new ArrayList<>(queryIndexList));
            log.info("{} 缓存索引已更新，共 {} 个分批", threadName, queryIndexList.size());
        } else {
            log.warn("{} 查询索引列表为空，未更新缓存索引", threadName);
        }
    }

    /**
     * 获取用户信息缓存
     * 分批加载，避免一次性加载全部数据到内存
     */
    public ArrayList<PushUserInfoDTO> getPushUserInfoCache(String cacheIndex) {
        String threadName = Thread.currentThread().getName();
        log.info("{} getPushUserInfoCache开始执行, cacheIndex: {}", threadName, cacheIndex);
        
        // 使用分段锁替代全局锁，减少锁竞争
        Object lock = getLock(cacheIndex);
        synchronized (lock) {
            // 先从缓存中查询
            ArrayList pushUserInfoList = get(cacheIndex);
            if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
                log.info("{} 从缓存命中数据, cacheIndex: {}, 大小: {}", threadName, cacheIndex, pushUserInfoList.size());
                // 返回一个新的列表副本，避免并发修改
                return new ArrayList<>(pushUserInfoList);
            }

            log.info("{} 缓存未命中, cacheIndex: {}, 返回空列表", threadName, cacheIndex);
            // 如果缓存中没有数据，返回空列表
            return new ArrayList<>();
        }
    }
}

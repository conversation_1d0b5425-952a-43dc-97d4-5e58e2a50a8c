package com.mira.job.service.handler.desk;

/**
 * frequency job handler
 *
 * <AUTHOR>
 */
public class FrequencyJobHandler {
    public static String getHandlerSource() {
        return "package com.mira.job.service.handler.desk;\n" +
                "\n" +
                "import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;\n" +
                "import com.mira.api.bluetooth.enums.CycleStatusEnum;\n" +
                "import com.mira.api.bluetooth.util.CycleDataUtil;\n" +
                "import com.mira.api.job.dto.NotificationPushCreateDTO;\n" +
                "import com.mira.api.job.dto.NotificationPushJobDTO;\n" +
                "import com.mira.api.message.dto.FirebasePushDTO;\n" +
                "import com.mira.core.consts.DatePatternConst;\n" +
                "import com.mira.core.util.JsonUtil;\n" +
                "import com.mira.core.util.LocalDateUtil;\n" +
                "import com.mira.core.util.ThreadPoolUtil;\n" +
                "import com.mira.core.util.ZoneDateUtil;\n" +
                "import com.mira.job.consts.dto.PushUserInfoDTO;\n" +
                "import com.mira.job.consts.enums.NotificationConditionEnum;\n" +
                "import com.mira.job.dal.dao.master.AdminNotificationJobDAO;\n" +
                "import com.mira.job.dal.dao.master.AppUserInfoDAO;\n" +
                "import com.mira.job.dal.dao.master.SysNotificationDefineDAO;\n" +
                "import com.mira.job.dal.entity.master.AdminNotificationJobEntity;\n" +
                "import com.mira.job.dal.entity.master.SysNotificationDefineEntity;\n" +
                "import com.mira.job.service.IJobService;\n" +
                "import com.mira.job.service.handler.desk.notification.NotificationJobHandler;\n" +
                "import com.mira.job.service.manager.DeskPushManager;\n" +
                "import com.mira.job.service.manager.JobManager;\n" +
                "import com.mira.job.service.util.FirebaseBuildUtil;\n" +
                "import com.xxl.job.core.context.XxlJobHelper;\n" +
                "import com.xxl.job.core.handler.IJobHandler;\n" +
                "import org.apache.commons.collections.CollectionUtils;\n" +
                "import org.apache.commons.lang3.StringUtils;\n" +
                "import org.slf4j.Logger;\n" +
                "import org.slf4j.LoggerFactory;\n" +
                "import org.springframework.stereotype.Component;\n" +
                "\n" +
                "import javax.annotation.Resource;\n" +
                "import java.util.ArrayList;\n" +
                "import java.util.HashMap;\n" +
                "import java.util.List;\n" +
                "import java.util.Map;\n" +
                "import java.util.concurrent.CompletableFuture;\n" +
                "import java.util.concurrent.CountDownLatch;\n" +
                "import java.util.concurrent.atomic.AtomicInteger;\n" +
                "\n" +
                "/**\n" +
                " * frequency job handler\n" +
                " *\n" +
                " * <AUTHOR> +
                " */\n" +
                "@Component\n" +
                "public class FrequencyJobHandlerSource extends IJobHandler {\n" +
                "    private static final Logger log = LoggerFactory.getLogger(FrequencyJobHandlerSource.class);\n" +
                "\n" +
                "    @Resource\n" +
                "    private AppUserInfoDAO appUserInfoDAO;\n" +
                "    @Resource\n" +
                "    private AdminNotificationJobDAO adminNotificationJobDAO;\n" +
                "    @Resource\n" +
                "    private SysNotificationDefineDAO sysNotificationDefineDAO;\n" +
                "    @Resource\n" +
                "    private DeskPushManager deskPushManager;\n" +
                "    @Resource\n" +
                "    private JobManager jobManager;\n" +
                "\n" +
                "    @Resource\n" +
                "    private IJobService jobService;\n" +
                "\n" +
                "    /**\n" +
                "     * 用户当地通知时间\n" +
                "     */\n" +
                "    private final static String LOCAL_NOTIFICATION_TIME_START = \"06:00:00\";\n" +
                "    private final static String LOCAL_NOTIFICATION_TIME_END = \"18:00:00\";\n" +
                "\n" +
                "    @Override\n" +
                "    public void execute() throws Exception {\n" +
                "        // 获取参数\n" +
                "        String jobParam = XxlJobHelper.getJobParam();\n" +
                "        Map<String, String> paramMap = JsonUtil.toObject(jobParam, Map.class);\n" +
                "        String taskId = paramMap.get(\"taskId\");\n" +
                "        String cycleStr = paramMap.get(\"cycle\");\n" +
                "        String dayInCycleStr = paramMap.get(\"dayInCycle\");\n" +
                "        // 处理参数\n" +
                "        List<Integer> cycleList = JsonUtil.toArray(cycleStr, Integer.class);\n" +
                "        List<Integer> dayInCycleList = JsonUtil.toArray(dayInCycleStr, Integer.class);\n" +
                "        // 筛选出的用户数量\n" +
                "        AtomicInteger atomicInteger = new AtomicInteger(0);\n" +
                "        // job记录\n" +
                "        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);\n" +
                "        NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(jobEntity.getJobJson(), NotificationPushCreateDTO.class);\n" +
                "        // 是否过期\n" +
                "        if (isExpire(pushCreateDTO)) {\n" +
                "            // 停止任务\n" +
                "            NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();\n" +
                "            notificationPushJobDTO.setTaskId(taskId);\n" +
                "            jobService.stopJob(notificationPushJobDTO);\n" +
                "            log.info(\"任务已过期，停止该任务:{}\", taskId);\n" +
                "            return;\n" +
                "        }\n" +
                "\n" +
                "        // 分批查询\n" +
                "        long pageSize = 0;\n" +
                "        long userCount = appUserInfoDAO.getCount();\n" +
                "        log.info(\"用户总数：{}\", userCount);\n" +
                "        int queryBatch = 10000;\n" +
                "        long cdCount = userCount % queryBatch == 0 ? userCount / queryBatch : userCount / queryBatch + 1;\n" +
                "        CountDownLatch cd = new CountDownLatch((int) cdCount);\n" +
                "        while (userCount > 0) {\n" +
                "            final long futurePageSize = pageSize;\n" +
                "            CompletableFuture.runAsync(() -> {\n" +
                "                List<PushUserInfoDTO> userInfoList = appUserInfoDAO.getBaseMapper().queryUserInfo(futurePageSize, queryBatch);\n" +
                "                if (CollectionUtils.isNotEmpty(userInfoList)) {\n" +
                "                    handle(jobEntity, pushCreateDTO, cycleList, dayInCycleList, userInfoList);\n" +
                "                    atomicInteger.addAndGet(userInfoList.size());\n" +
                "                }\n" +
                "                cd.countDown();\n" +
                "            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {\n" +
                "                log.error(\"get push user info error occurred\", ex);\n" +
                "                return null;\n" +
                "            });\n" +
                "\n" +
                "            pageSize += queryBatch;\n" +
                "            userCount -= queryBatch;\n" +
                "        }\n" +
                "\n" +
                "        // wait future\n" +
                "        try {\n" +
                "            cd.await();\n" +
                "            log.info(\"满足触发条件的用户总数：{}\", atomicInteger.get());\n" +
                "        } catch (InterruptedException ex) {\n" +
                "            log.error(ex.getMessage(), ex);\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "    private boolean isExpire(NotificationPushCreateDTO pushCreateDTO) {\n" +
                "        if (StringUtils.isNotBlank(pushCreateDTO.getExpireTime())) {\n" +
                "            String nowTime = ZoneDateUtil.format(\"America/Los_Angeles\", System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);\n" +
                "            return LocalDateUtil.after(nowTime, pushCreateDTO.getExpireTime(), DatePatternConst.DATE_TIME_PATTERN);\n" +
                "        }\n" +
                "\n" +
                "        return false;\n" +
                "    }\n" +
                "\n" +
                "    private void handle(AdminNotificationJobEntity notificationJobEntity,\n" +
                "                        NotificationPushCreateDTO pushCreateDTO,\n" +
                "                        List<Integer> cycleList, List<Integer> dayInCycleList,\n" +
                "                        List<PushUserInfoDTO> allUserInfo) {\n" +
                "        // filter user info\n" +
                "        NotificationJobHandler.handle(pushCreateDTO, allUserInfo);\n" +
                "\n" +
                "        // 触发器\n" +
                "        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>(); // 删除没有命中触发条件的用户\n" +
                "        for (PushUserInfoDTO userInfo : allUserInfo) {\n" +
                "            try {\n" +
                "                Long userId = userInfo.getUserId();\n" +
                "                // today\n" +
                "                String userLocalDate = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);\n" +
                "                // 判断时间是否在设定的通知时间段内\n" +
                "                if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {\n" +
                "                    waitDeleteList.add(userInfo);\n" +
                "                    continue;\n" +
                "                }\n" +
                "                // 用户当天已经推送过的，不再推送\n" +
                "                if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.DESK_CUSTOM_NOTIFICATION_JOB, userLocalDate)) {\n" +
                "                    waitDeleteList.add(userInfo);\n" +
                "                    continue;\n" +
                "                }\n" +
                "\n" +
                "                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);\n" +
                "                // 用户当前周期，实周期，是否在选择的列表中\n" +
                "                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);\n" +
                "                if (notMatchCycle(currentCycleData, cycleList)) {\n" +
                "                    waitDeleteList.add(userInfo);\n" +
                "                    continue;\n" +
                "                }\n" +
                "                // 排除当天不在CD列表中的用户\n" +
                "                if (notMatchCD(userLocalDate, currentCycleData, dayInCycleList)) {\n" +
                "                    waitDeleteList.add(userInfo);\n" +
                "                    continue;\n" +
                "                }\n" +
                "                // 当日已推送标记\n" +
                "                jobManager.markUserCurrentDayPush(userId, NotificationConditionEnum.DESK_CUSTOM_NOTIFICATION_JOB, userLocalDate);\n" +
                "            } catch (Exception e) {\n" +
                "                log.error(\"[Desk Job] user:{} handle cycle error.\", userInfo.getUserId(), e);\n" +
                "            }\n" +
                "        }\n" +
                "        if (CollectionUtils.isNotEmpty(waitDeleteList)) {\n" +
                "            allUserInfo.removeAll(waitDeleteList);\n" +
                "        }\n" +
                "\n" +
                "        // push\n" +
                "        log.info(\"condition filter user size:{}\", allUserInfo.size());\n" +
                "        push(notificationJobEntity, pushCreateDTO, allUserInfo);\n" +
                "    }\n" +
                "\n" +
                "    private boolean notMatchCycle(CycleDataDTO currentCycleData, List<Integer> cycleList) {\n" +
                "        if (CollectionUtils.isEmpty(cycleList)) {\n" +
                "            return false;\n" +
                "        }\n" +
                "        if (currentCycleData == null\n" +
                "                || currentCycleData.getCycle_status() != CycleStatusEnum.REAL_CYCLE.getStatus()) {\n" +
                "            return true;\n" +
                "        }\n" +
                "\n" +
                "        int cycleIndex = currentCycleData.getCycle_index() + 1;\n" +
                "        if (cycleList.contains(7) && cycleIndex > 6) { // 表示>6的选项\n" +
                "            return false;\n" +
                "        }\n" +
                "        if (!cycleList.contains(cycleIndex)) {\n" +
                "            return true;\n" +
                "        }\n" +
                "        return false;\n" +
                "    }\n" +
                "\n" +
                "    private boolean notMatchCD(String today, CycleDataDTO currentCycleData, List<Integer> dayInCycleList) {\n" +
                "        if (CollectionUtils.isEmpty(dayInCycleList)) {\n" +
                "            return false;\n" +
                "        }\n" +
                "        String datePeriodStart = currentCycleData.getDate_period_start();\n" +
                "        if (StringUtils.isBlank(datePeriodStart)) {\n" +
                "            return true;\n" +
                "        }\n" +
                "        int cdIndex = LocalDateUtil.minusToDay(today, datePeriodStart);\n" +
                "        if (cdIndex < 0) {\n" +
                "            return true;\n" +
                "        }\n" +
                "        List<String> cycleCdIndexList = currentCycleData.getCycle_cd_index();\n" +
                "        if (cdIndex >= cycleCdIndexList.size()) {\n" +
                "            return true;\n" +
                "        }\n" +
                "\n" +
                "        if (dayInCycleList.contains(36) && (cdIndex + 1) > 35) { // 表示>35的选项\n" +
                "            return false;\n" +
                "        }\n" +
                "        if (!dayInCycleList.contains(cdIndex + 1)) {\n" +
                "            return true;\n" +
                "        }\n" +
                "        return false;\n" +
                "    }\n" +
                "\n" +
                "    private void push(AdminNotificationJobEntity notificationJobEntity,\n" +
                "                      NotificationPushCreateDTO pushCreateDTO,\n" +
                "                      List<PushUserInfoDTO> allUserInfo) {\n" +
                "        // push\n" +
                "        SysNotificationDefineEntity defineEntity = sysNotificationDefineDAO\n" +
                "                .getByDefineId(notificationJobEntity.getDefineId(), \"en-us\");\n" +
                "        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = getFirebasePushMap(defineEntity, allUserInfo);\n" +
                "        Map<Long, Long> expireTimestampMap = getExpireTimestampMap(allUserInfo, pushCreateDTO);\n" +
                "        if (pushCreateDTO.getSilent() == 1) {\n" +
                "            deskPushManager.firebasePushSilent(firebasePushMap, expireTimestampMap);\n" +
                "        } else {\n" +
                "            deskPushManager.firebasePush(firebasePushMap, expireTimestampMap);\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "    private Map<FirebasePushDTO, List<PushUserInfoDTO>> getFirebasePushMap(SysNotificationDefineEntity sysNotificationDefine,\n" +
                "                                                                           List<PushUserInfoDTO> filterResult) {\n" +
                "        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(sysNotificationDefine, false);\n" +
                "        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = new HashMap<>();\n" +
                "        firebasePushMap.putIfAbsent(firebasePushDTO, filterResult);\n" +
                "\n" +
                "        return firebasePushMap;\n" +
                "    }\n" +
                "\n" +
                "    private Map<Long, Long> getExpireTimestampMap(List<PushUserInfoDTO> allUserInfo, NotificationPushCreateDTO pushCreateDTO) {\n" +
                "        // 过期时间\n" +
                "        String expireTime = pushCreateDTO.getExpireTime();\n" +
                "        Map<Long, Long> expireTimestampMap = new HashMap<>();\n" +
                "\n" +
                "        if (StringUtils.isBlank(expireTime)) {\n" +
                "            return expireTimestampMap;\n" +
                "        }\n" +
                "\n" +
                "        for (PushUserInfoDTO userInfoDTO : allUserInfo) {\n" +
                "            Long expireTimestamp = ZoneDateUtil.timestamp(userInfoDTO.getTimeZone(), expireTime, DatePatternConst.DATE_TIME_PATTERN);\n" +
                "            expireTimestampMap.put(userInfoDTO.getUserId(), expireTimestamp);\n" +
                "        }\n" +
                "\n" +
                "        return expireTimestampMap;\n" +
                "    }\n" +
                "}\n";
    }
}

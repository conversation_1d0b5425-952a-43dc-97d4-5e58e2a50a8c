package com.mira.job.service.manager;

import com.mira.core.util.ThreadPoolUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.properties.JobProperties;
import com.mira.redis.cache.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存管理器 - 用于替代Ehcache实现
 * 使用Redis存储大量数据，避免堆内存溢出
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisPushUserInfoCacheManager {
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private JobProperties jobProperties;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 缓存键前缀
     */
    private final String CACHE_PREFIX = "push:userinfo:data:";
    /**
     * 缓存索引键
     */
    private final String CACHE_INDEX_KEY = "push:userinfo:index";
    /**
     * 缓存过期时间（小时）
     */
    private final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 将数据存入Redis缓存
     */
    private void put(String key, ArrayList<PushUserInfoDTO> value) {
        try {
            String cacheKey = CACHE_PREFIX + key;
            redisComponent.setEx(cacheKey, value, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("数据已存入Redis缓存: {}", cacheKey);
        } catch (Exception e) {
            log.error("存储数据到Redis缓存异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 从Redis缓存获取数据
     */
    @SuppressWarnings("unchecked")
    private ArrayList<PushUserInfoDTO> get(String key) {
        try {
            String cacheKey = CACHE_PREFIX + key;
            Object value = redisComponent.get(cacheKey);
            if (value instanceof ArrayList) {
                return (ArrayList<PushUserInfoDTO>) value;
            }
            return null;
        } catch (Exception e) {
            log.error("从Redis缓存获取数据异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从Redis缓存中移除数据
     */
    private void remove(String key) {
        try {
            String cacheKey = CACHE_PREFIX + key;
            redisComponent.delete(cacheKey);
            log.debug("已从Redis缓存中移除数据: {}", cacheKey);
        } catch (Exception e) {
            log.error("从Redis缓存移除数据异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取缓存索引列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getCacheIndex() {
        ArrayList<String> cacheIndexList = null;
        try {
            Object value = redisComponent.get(CACHE_INDEX_KEY);
            if (value instanceof ArrayList) {
                cacheIndexList = (ArrayList<String>) value;
            }
        } catch (Exception e) {
            log.error("获取缓存索引异常: {}", e.getMessage(), e);
        }

        // 检查缓存开关
        if (0 == jobProperties.getCacheSwitch()) {
            remove(CACHE_INDEX_KEY);
            if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                cacheIndexList = null;
            }
        }

        // 缓存不存在，初始化缓存
        if (CollectionUtils.isEmpty(cacheIndexList)) {
            synchronized (RedisPushUserInfoCacheManager.class) {
                // 双重检查
                try {
                    Object value = redisComponent.get(CACHE_INDEX_KEY);
                    if (value instanceof ArrayList) {
                        cacheIndexList = (ArrayList<String>) value;
                    }
                } catch (Exception e) {
                    log.error("获取缓存索引异常: {}", e.getMessage(), e);
                }

                if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                    return cacheIndexList;
                }
                getPushUserInfoCache("-1");
                try {
                    Object value = redisComponent.get(CACHE_INDEX_KEY);
                    if (value instanceof ArrayList) {
                        return (ArrayList<String>) value;
                    }
                } catch (Exception e) {
                    log.error("获取缓存索引异常: {}", e.getMessage(), e);
                }
                return new ArrayList<>();
            }
        }
        return cacheIndexList;
    }

    /**
     * 获取用户信息缓存
     * 分批加载，避免一次性加载全部数据到内存
     */
    public ArrayList<PushUserInfoDTO> getPushUserInfoCache(String cacheIndex) {
        // 先从缓存中查询
        ArrayList<PushUserInfoDTO> pushUserInfoList = get(cacheIndex);
        if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
            return pushUserInfoList;
        }

        // 缓存不存在，从数据库查询
        synchronized (RedisPushUserInfoCacheManager.class) {
            // 双重检查
            pushUserInfoList = get(cacheIndex);
            if (CollectionUtils.isNotEmpty(pushUserInfoList)) {
                return pushUserInfoList;
            }

            // 分批查询参数设置
            int queryBatch = 5000;
            long pageSize = 0;
            long recordCount = appUserInfoDAO.getCount();
            log.info("用户记录总数: {}", recordCount);

            // 计算分批数量
            long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
            CountDownLatch cd = new CountDownLatch((int) cdCount);
            long queryIndex = cdCount;
            ArrayList<String> queryIndexList = new ArrayList<>();

            // 分批处理
            while (recordCount > 0) {
                long finalPageSize = pageSize;
                long finalQueryIndex = queryIndex;
                CompletableFuture.runAsync(() -> {
                    try {
                        // 查询数据库
                        ArrayList<PushUserInfoDTO> recordList = appUserInfoDAO.getBaseMapper().queryUserInfo(finalPageSize, queryBatch);
                        if (CollectionUtils.isNotEmpty(recordList)) {
                            String key = String.valueOf(finalQueryIndex);
                            queryIndexList.add(key);
                            // 将数据存入缓存
                            put(key, recordList);
                            log.debug("成功缓存用户数据分批: {}, 数量: {}", key, recordList.size());
                        }
                    } catch (Exception e) {
                        log.error("查询用户信息异常: {}", e.getMessage(), e);
                    } finally {
                        cd.countDown();
                    }
                }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                    log.error("获取用户信息异常", ex);
                    return null;
                });

                pageSize += queryBatch;
                recordCount -= queryBatch;
                queryIndex--;
            }

            // 等待所有异步任务完成
            try {
                cd.await();
                log.info("所有用户数据缓存完成，共 {} 批", cdCount);
            } catch (InterruptedException ex) {
                log.error("等待缓存加载完成时被中断: {}", ex.getMessage(), ex);
            }

            // 缓存索引
            if (CollectionUtils.isNotEmpty(queryIndexList)) {
                try {
                    redisComponent.setEx(CACHE_INDEX_KEY, queryIndexList, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
                    log.info("缓存索引已更新，共 {} 个分批", queryIndexList.size());
                } catch (Exception e) {
                    log.error("更新缓存索引异常: {}", e.getMessage(), e);
                }
            }
        }

        return get(cacheIndex);
    }

    /**
     * 清空所有缓存
     * 在内存压力大时可以调用此方法释放内存
     */
    public void clearAllCache() {
        try {
            Object value = redisComponent.get(CACHE_INDEX_KEY);
            if (value instanceof ArrayList) {
                ArrayList<String> cacheIndexList = (ArrayList<String>) value;
                if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                    for (String index : cacheIndexList) {
                        remove(index);
                    }
                    log.info("已清空所有Redis缓存数据，共 {} 个分批", cacheIndexList.size());
                }
            }
            remove(CACHE_INDEX_KEY);
        } catch (Exception e) {
            log.error("清空Redis缓存异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取缓存状态信息
     * 返回缓存中的分批数量和估计的数据量
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        try {
            Object value = redisComponent.get(CACHE_INDEX_KEY);
            if (value instanceof ArrayList) {
                ArrayList<String> cacheIndexList = (ArrayList<String>) value;
                int batchCount = CollectionUtils.isEmpty(cacheIndexList) ? 0 : cacheIndexList.size();
                status.put("缓存分批数", batchCount);

                // 估算总数据量
                long totalRecords = 0;
                if (CollectionUtils.isNotEmpty(cacheIndexList)) {
                    for (String index : cacheIndexList) {
                        ArrayList<PushUserInfoDTO> records = get(index);
                        if (records != null) {
                            totalRecords += records.size();
                        }
                    }
                }
                status.put("缓存总条目", totalRecords);
                status.put("缓存状态", batchCount > 0 ? "正常" : "未初始化");
                status.put("缓存类型", "Redis");
                status.put("过期时间", CACHE_EXPIRE_HOURS + "小时");
            } else {
                status.put("缓存状态", "未初始化");
                status.put("缓存类型", "Redis");
            }
        } catch (Exception e) {
            log.error("获取Redis缓存状态异常: {}", e.getMessage(), e);
            status.put("缓存状态", "异常: " + e.getMessage());
            status.put("缓存类型", "Redis");
        }
        return status;
    }
}

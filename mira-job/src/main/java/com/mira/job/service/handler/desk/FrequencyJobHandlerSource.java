//package com.mira.job.service.handler.desk;
//
//import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
//import com.mira.api.bluetooth.enums.CycleStatusEnum;
//import com.mira.api.bluetooth.util.CycleDataUtil;
//import com.mira.api.job.dto.NotificationPushCreateDTO;
//import com.mira.api.job.dto.NotificationPushJobDTO;
//import com.mira.api.message.dto.FirebasePushDTO;
//import com.mira.core.consts.DatePatternConst;
//import com.mira.core.util.JsonUtil;
//import com.mira.core.util.LocalDateUtil;
//import com.mira.core.util.ThreadPoolUtil;
//import com.mira.core.util.ZoneDateUtil;
//import com.mira.job.consts.dto.PushUserInfoDTO;
//import com.mira.job.consts.enums.NotificationConditionEnum;
//import com.mira.job.dal.dao.master.AdminNotificationJobDAO;
//import com.mira.job.dal.dao.master.AppUserInfoDAO;
//import com.mira.job.dal.dao.master.SysNotificationDefineDAO;
//import com.mira.job.dal.entity.master.AdminNotificationJobEntity;
//import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
//import com.mira.job.service.IJobService;
//import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
//import com.mira.job.service.manager.DeskPushManager;
//import com.mira.job.service.manager.JobManager;
//import com.mira.job.service.util.FirebaseBuildUtil;
//import com.xxl.job.core.context.XxlJobHelper;
//import com.xxl.job.core.handler.IJobHandler;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.atomic.AtomicInteger;
//
///**
// * frequency job handler
// *
// * <AUTHOR>
// */
//@Component
//public class FrequencyJobHandlerSource extends IJobHandler {
//    private static final Logger log = LoggerFactory.getLogger(FrequencyJobHandlerSource.class);
//
//    @Resource
//    private AppUserInfoDAO appUserInfoDAO;
//    @Resource
//    private AdminNotificationJobDAO adminNotificationJobDAO;
//    @Resource
//    private SysNotificationDefineDAO sysNotificationDefineDAO;
//    @Resource
//    private DeskPushManager deskPushManager;
//    @Resource
//    private JobManager jobManager;
//
//    @Resource
//    private IJobService jobService;
//
//    /**
//     * 用户当地通知时间
//     */
//    private final static String LOCAL_NOTIFICATION_TIME_START = "06:00:00";
//    private final static String LOCAL_NOTIFICATION_TIME_END = "18:00:00";
//
//    @Override
//    public void execute() throws Exception {
//        // 获取参数
//        String jobParam = XxlJobHelper.getJobParam();
//        Map<String, String> paramMap = JsonUtil.toObject(jobParam, Map.class);
//        String taskId = paramMap.get("taskId");
//        String cycleStr = paramMap.get("cycle");
//        String dayInCycleStr = paramMap.get("dayInCycle");
//        // 处理参数
//        List<Integer> cycleList = JsonUtil.toArray(cycleStr, Integer.class);
//        List<Integer> dayInCycleList = JsonUtil.toArray(dayInCycleStr, Integer.class);
//        // 筛选出的用户数量
//        AtomicInteger atomicInteger = new AtomicInteger(0);
//        // job记录
//        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);
//        NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(jobEntity.getJobJson(), NotificationPushCreateDTO.class);
//        // 是否过期
//        if (isExpire(pushCreateDTO)) {
//            // 停止任务
//            NotificationPushJobDTO notificationPushJobDTO = new NotificationPushJobDTO();
//            notificationPushJobDTO.setTaskId(taskId);
//            jobService.stopJob(notificationPushJobDTO);
//            log.info("任务已过期，停止该任务:{}", taskId);
//            return;
//        }
//
//        // 分批查询
//        long pageSize = 0;
//        long userCount = appUserInfoDAO.getCount();
//        log.info("用户总数：{}", userCount);
//        int queryBatch = 10000;
//        long cdCount = userCount % queryBatch == 0 ? userCount / queryBatch : userCount / queryBatch + 1;
//        CountDownLatch cd = new CountDownLatch((int) cdCount);
//        while (userCount > 0) {
//            final long futurePageSize = pageSize;
//            CompletableFuture.runAsync(() -> {
//                List<PushUserInfoDTO> userInfoList = appUserInfoDAO.getBaseMapper().queryUserInfo(futurePageSize, queryBatch);
//                if (CollectionUtils.isNotEmpty(userInfoList)) {
//                    handle(jobEntity, pushCreateDTO, cycleList, dayInCycleList, userInfoList);
//                    atomicInteger.addAndGet(userInfoList.size());
//                }
//                cd.countDown();
//            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
//                log.error("get push user info error occurred", ex);
//                return null;
//            });
//
//            pageSize += queryBatch;
//            userCount -= queryBatch;
//        }
//
//        // wait future
//        try {
//            cd.await();
//            log.info("满足触发条件的用户总数：{}", atomicInteger.get());
//        } catch (InterruptedException ex) {
//            log.error(ex.getMessage(), ex);
//        }
//    }
//
//    private boolean isExpire(NotificationPushCreateDTO pushCreateDTO) {
//        if (StringUtils.isNotBlank(pushCreateDTO.getExpireTime())) {
//            String nowTime = ZoneDateUtil.format("America/Los_Angeles", System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
//            return LocalDateUtil.after(nowTime, pushCreateDTO.getExpireTime(), DatePatternConst.DATE_TIME_PATTERN);
//        }
//
//        return false;
//    }
//
//    private void handle(AdminNotificationJobEntity notificationJobEntity,
//                        NotificationPushCreateDTO pushCreateDTO,
//                        List<Integer> cycleList, List<Integer> dayInCycleList,
//                        List<PushUserInfoDTO> allUserInfo) {
//        // filter user info
//        NotificationJobHandler.handle(pushCreateDTO, allUserInfo);
//
//        // 触发器
//        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>(); // 删除没有命中触发条件的用户
//        for (PushUserInfoDTO userInfo : allUserInfo) {
//            try {
//                Long userId = userInfo.getUserId();
//                // today
//                String userLocalDate = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
//                // 判断时间是否在设定的通知时间段内
//                if (!LocalDateUtil.isBetweenTime(userLocalDate, LOCAL_NOTIFICATION_TIME_START, LOCAL_NOTIFICATION_TIME_END)) {
//                    waitDeleteList.add(userInfo);
//                    continue;
//                }
//                // 用户当天已经推送过的，不再推送
//                if (jobManager.checkUserCurrentDayPush(userId, NotificationConditionEnum.DESK_CUSTOM_NOTIFICATION_JOB, userLocalDate)) {
//                    waitDeleteList.add(userInfo);
//                    continue;
//                }
//
//                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
//                // 用户当前周期，实周期，是否在选择的列表中
//                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(userLocalDate, cycleDataDTOS);
//                if (notMatchCycle(currentCycleData, cycleList)) {
//                    waitDeleteList.add(userInfo);
//                    continue;
//                }
//                // 排除当天不在CD列表中的用户
//                if (notMatchCD(userLocalDate, currentCycleData, dayInCycleList)) {
//                    waitDeleteList.add(userInfo);
//                    continue;
//                }
//                // 当日已推送标记
//                jobManager.markUserCurrentDayPush(userId, NotificationConditionEnum.DESK_CUSTOM_NOTIFICATION_JOB, userLocalDate);
//            } catch (Exception e) {
//                log.error("[Desk Job] user:{} handle cycle error.", userInfo.getUserId(), e);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
//            allUserInfo.removeAll(waitDeleteList);
//        }
//
//        // push
//        log.info("condition filter user size:{}", allUserInfo.size());
//        push(notificationJobEntity, pushCreateDTO, allUserInfo);
//    }
//
//    private boolean notMatchCycle(CycleDataDTO currentCycleData, List<Integer> cycleList) {
//        if (CollectionUtils.isEmpty(cycleList)) {
//            return false;
//        }
//        if (currentCycleData == null
//                || currentCycleData.getCycle_status() != CycleStatusEnum.REAL_CYCLE.getStatus()) {
//            return true;
//        }
//
//        int cycleIndex = currentCycleData.getCycle_index() + 1;
//        if (cycleList.contains(7) && cycleIndex > 6) { // 表示>6的选项
//            return false;
//        }
//        if (!cycleList.contains(cycleIndex)) {
//            return true;
//        }
//        return false;
//    }
//
//    private boolean notMatchCD(String today, CycleDataDTO currentCycleData, List<Integer> dayInCycleList) {
//        if (CollectionUtils.isEmpty(dayInCycleList)) {
//            return false;
//        }
//        String datePeriodStart = currentCycleData.getDate_period_start();
//        if (StringUtils.isBlank(datePeriodStart)) {
//            return true;
//        }
//        int cdIndex = LocalDateUtil.minusToDay(today, datePeriodStart);
//        if (cdIndex < 0) {
//            return true;
//        }
//        List<String> cycleCdIndexList = currentCycleData.getCycle_cd_index();
//        if (cdIndex >= cycleCdIndexList.size()) {
//            return true;
//        }
//
//        if (dayInCycleList.contains(36) && (cdIndex + 1) > 35) { // 表示>35的选项
//            return false;
//        }
//        if (!dayInCycleList.contains(cdIndex + 1)) {
//            return true;
//        }
//        return false;
//    }
//
//    private void push(AdminNotificationJobEntity notificationJobEntity,
//                      NotificationPushCreateDTO pushCreateDTO,
//                      List<PushUserInfoDTO> allUserInfo) {
//        // push
//        SysNotificationDefineEntity defineEntity = sysNotificationDefineDAO
//                .getByDefineId(notificationJobEntity.getDefineId(), "en-us");
//        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = getFirebasePushMap(defineEntity, allUserInfo);
//        Map<Long, Long> expireTimestampMap = getExpireTimestampMap(allUserInfo, pushCreateDTO);
//        if (pushCreateDTO.getSilent() == 1) {
//            deskPushManager.firebasePushSilent(firebasePushMap, expireTimestampMap);
//        } else {
//            deskPushManager.firebasePush(firebasePushMap, expireTimestampMap);
//        }
//    }
//
//    private Map<FirebasePushDTO, List<PushUserInfoDTO>> getFirebasePushMap(SysNotificationDefineEntity sysNotificationDefine,
//                                                                           List<PushUserInfoDTO> filterResult) {
//        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(sysNotificationDefine, false);
//        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = new HashMap<>();
//        firebasePushMap.putIfAbsent(firebasePushDTO, filterResult);
//
//        return firebasePushMap;
//    }
//
//    private Map<Long, Long> getExpireTimestampMap(List<PushUserInfoDTO> allUserInfo, NotificationPushCreateDTO pushCreateDTO) {
//        // 过期时间
//        String expireTime = pushCreateDTO.getExpireTime();
//        Map<Long, Long> expireTimestampMap = new HashMap<>();
//
//        if (StringUtils.isBlank(expireTime)) {
//            return expireTimestampMap;
//        }
//
//        for (PushUserInfoDTO userInfoDTO : allUserInfo) {
//            Long expireTimestamp = ZoneDateUtil.timestamp(userInfoDTO.getTimeZone(), expireTime, DatePatternConst.DATE_TIME_PATTERN);
//            expireTimestampMap.put(userInfoDTO.getUserId(), expireTimestamp);
//        }
//
//        return expireTimestampMap;
//    }
//}

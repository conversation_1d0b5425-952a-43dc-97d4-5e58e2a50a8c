package com.mira.web.util;

import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp 工具类
 *
 * <AUTHOR>
 */
public class OkHttpUtil {
    /**
     * JSON媒体类型
     */
    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    /**
     * 默认连接超时时间（秒）
     */
    private static final int DEFAULT_CONNECT_TIMEOUT = 10;

    /**
     * 默认读取超时时间（秒）
     */
    private static final int DEFAULT_READ_TIMEOUT = 30;

    /**
     * 默认写入超时时间（秒）
     */
    private static final int DEFAULT_WRITE_TIMEOUT = 10;

    /**
     * 默认HTTP客户端
     */
    private static final OkHttpClient DEFAULT_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
            .build();

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        return executeRequest(request);
    }

    /**
     * 发送带请求头的GET请求
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String get(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .get();

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        return executeRequest(requestBuilder.build());
    }

    /**
     * 发送POST请求，请求体为JSON格式
     *
     * @param url  请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String post(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 发送带请求头的POST请求，请求体为JSON格式
     *
     * @param url     请求URL
     * @param json    JSON字符串
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String post(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        return executeRequest(requestBuilder.build());
    }

    /**
     * 发送PUT请求，请求体为JSON格式
     *
     * @param url  请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String put(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .put(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 发送DELETE请求
     *
     * @param url 请求URL
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String delete(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .delete()
                .build();
        return executeRequest(request);
    }

    /**
     * 发送DELETE请求，请求体为JSON格式
     *
     * @param url  请求URL
     * @param json JSON字符串
     * @return 响应内容
     * @throws IOException IO异常
     */
    public static String delete(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .delete(body)
                .build();
        return executeRequest(request);
    }

    /**
     * 执行HTTP请求
     *
     * @param request 请求对象
     * @return 响应内容
     * @throws IOException IO异常
     */
    private static String executeRequest(Request request) throws IOException {
        // 使用try-with-resources自动关闭响应体
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response.code() + " " + response.message());
            }

            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                return responseBody.string();
            }
            return null;
        }
    }
}
